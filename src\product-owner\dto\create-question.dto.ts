import { IsString, IsNotEmpty, IsArray, ValidateNested, IsOptional, IsUUID, IsInt, Min, Max, ArrayMinSize } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateQuestionOptionDto {
  @IsString()
  @IsNotEmpty()
  option_text: string;

  @IsInt()
  @Min(0)
  @Max(100)
  option_value: number;

  @IsUUID()
  @IsNotEmpty()
  categoryId: string;
}

export class CreateQuestionDto {
  @IsString()
  @IsNotEmpty()
  question_text: string;

  @IsArray()
  @ValidateNested({ each: true })
  @ArrayMinSize(1)
  @Type(() => CreateQuestionOptionDto)
  options: CreateQuestionOptionDto[];

  @IsOptional()
  @IsUUID()
  createdBy?: string;
}