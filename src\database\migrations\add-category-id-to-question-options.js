'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    console.log('🚀 Starting migration: Adding category_id to QuestionOptions table...');

    try {
      // Check if category_id column already exists
      const tableDescription = await queryInterface.describeTable('QuestionOptions');

      if (tableDescription.category_id) {
        console.log('⚠️ category_id column already exists, skipping column creation');
      } else {
        // Add category_id column to QuestionOptions table
        await queryInterface.addColumn('QuestionOptions', 'category_id', {
          type: Sequelize.UUID,
          allowNull: true, // Initially nullable to allow existing data
          references: {
            model: 'AnswerCategories',
            key: 'id'
          },
          onUpdate: 'CASCADE',
          onDelete: 'SET NULL',
          comment: 'Foreign key to AnswerCategories table'
        });

        console.log('✅ category_id column added to QuestionOptions table');
      }

      // Check if we need to update existing question options
      const existingOptionsCount = await queryInterface.sequelize.query(
        'SELECT COUNT(*) as count FROM QuestionOptions WHERE category_id IS NULL',
        { type: Sequelize.QueryTypes.SELECT }
      );

      if (existingOptionsCount[0].count > 0) {
        // Update existing question options with default categories based on common patterns
        console.log('🔧 Updating existing question options with default categories...');

        // Map common option texts to categories (case-insensitive)
        const categoryMappings = [
          // Very Positive
          { patterns: ['excellent', 'outstanding', 'strongly agree', 'very satisfied', 'love it', 'amazing'], categoryId: '11111111-1111-1111-1111-111111111111' },

          // Positive
          { patterns: ['good', 'agree', 'satisfied', 'like it', 'yes', 'positive', 'happy'], categoryId: '22222222-2222-2222-2222-222222222222' },

          // Neutral
          { patterns: ['okay', 'ok', 'neutral', 'neither', 'average', 'maybe', 'unsure'], categoryId: '33333333-3333-3333-3333-333333333333' },

          // Negative
          { patterns: ['bad', 'disagree', 'unsatisfied', 'dislike', 'no', 'negative', 'unhappy'], categoryId: '44444444-4444-4444-4444-444444444444' },

          // Very Negative
          { patterns: ['terrible', 'awful', 'strongly disagree', 'very unsatisfied', 'hate it', 'horrible'], categoryId: '55555555-5555-5555-5555-555555555555' }
        ];

        // Apply mappings
        for (const mapping of categoryMappings) {
          for (const pattern of mapping.patterns) {
            await queryInterface.sequelize.query(`
              UPDATE QuestionOptions
              SET category_id = :categoryId
              WHERE LOWER(option_text) LIKE :pattern
              AND category_id IS NULL
            `, {
              replacements: {
                categoryId: mapping.categoryId,
                pattern: `%${pattern}%`
              }
            });
          }
        }

        // Set remaining unmapped options to NEUTRAL as default
        await queryInterface.sequelize.query(`
          UPDATE QuestionOptions
          SET category_id = '33333333-3333-3333-3333-333333333333'
          WHERE category_id IS NULL
        `);

        console.log('✅ Existing question options updated with categories');
      } else {
        console.log('✅ All question options already have categories assigned');
      }

      // Check if we need to make the column NOT NULL
      const nullCategoryCount = await queryInterface.sequelize.query(
        'SELECT COUNT(*) as count FROM QuestionOptions WHERE category_id IS NULL',
        { type: Sequelize.QueryTypes.SELECT }
      );

      if (nullCategoryCount[0].count === 0) {
        // Only make NOT NULL if all records have categories
        try {
          await queryInterface.changeColumn('QuestionOptions', 'category_id', {
            type: Sequelize.UUID,
            allowNull: false,
            references: {
              model: 'AnswerCategories',
              key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'RESTRICT', // Changed to RESTRICT to prevent accidental category deletion
            comment: 'Foreign key to AnswerCategories table'
          });
          console.log('✅ category_id column set to NOT NULL');
        } catch (error) {
          console.log('⚠️ Could not set category_id to NOT NULL, but column exists and is populated');
        }
      } else {
        console.log('⚠️ Some question options still have NULL category_id, keeping column nullable');
      }
      console.log('🎉 Migration completed: QuestionOptions now linked to AnswerCategories!');
    } catch (error) {
      console.error('❌ Error during QuestionOptions migration:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    console.log('🔄 Rolling back migration: Removing category_id from QuestionOptions...');
    await queryInterface.removeColumn('QuestionOptions', 'category_id');
    console.log('✅ category_id column removed from QuestionOptions');
    console.log('🔙 Migration rollback completed');
  }
};
