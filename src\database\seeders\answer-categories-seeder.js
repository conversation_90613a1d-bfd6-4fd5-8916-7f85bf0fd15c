'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    console.log('🌱 Starting seeder: Populating AnswerCategories...');

    try {
      // Check if categories already exist
      const existingCategories = await queryInterface.sequelize.query(
        'SELECT COUNT(*) as count FROM AnswerCategories WHERE isDeleted = 0',
        { type: Sequelize.QueryTypes.SELECT }
      );

      if (existingCategories[0].count > 0) {
        console.log('✅ Answer categories already exist, skipping seeder');
        return;
      }

      // Insert predefined answer categories
      await queryInterface.bulkInsert('AnswerCategories', [
        {
          id: '11111111-1111-1111-1111-111111111111',
          name: 'VERY_POSITIVE',
          display_name: 'Very Positive',
          color_code: '#28a745',
          sort_order: 1,
          description: 'Responses indicating highest satisfaction, agreement, or positive sentiment. Examples: Excellent, Outstanding, Strongly Agree, Love it, Amazing',
          isActive: true,
          isDeleted: false,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: '22222222-2222-2222-2222-222222222222',
          name: 'P<PERSON>IT<PERSON>',
          display_name: 'Positive',
          color_code: '#17a2b8',
          sort_order: 2,
          description: 'Responses indicating good satisfaction, agreement, or positive sentiment. Examples: Good, Agree, Satisfied, Like it, Yes',
          isActive: true,
          isDeleted: false,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: '33333333-3333-3333-3333-333333333333',
          name: 'NEUTRAL',
          display_name: 'Neutral',
          color_code: '#ffc107',
          sort_order: 3,
          description: 'Responses indicating neutral sentiment or no strong opinion. Examples: Okay, Neither Agree nor Disagree, Average, Maybe, Unsure',
          isActive: true,
          isDeleted: false,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: '44444444-4444-4444-4444-444444444444',
          name: 'NEGATIVE',
          display_name: 'Negative',
          color_code: '#fd7e14',
          sort_order: 4,
          description: 'Responses indicating dissatisfaction, disagreement, or negative sentiment. Examples: Bad, Disagree, Unsatisfied, Dislike, No',
          isActive: true,
          isDeleted: false,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: '55555555-5555-5555-5555-555555555555',
          name: 'VERY_NEGATIVE',
          display_name: 'Very Negative',
          color_code: '#dc3545',
          sort_order: 5,
          description: 'Responses indicating highest dissatisfaction, strong disagreement, or very negative sentiment. Examples: Terrible, Awful, Strongly Disagree, Hate it, Horrible',
          isActive: true,
          isDeleted: false,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ]);

      console.log('✅ Answer categories seeded successfully');

      // Update existing question options with default categories if they exist
      console.log('🔧 Updating existing question options with default categories...');
      
      // Check if QuestionOptions table exists and has data
      const questionOptionsExist = await queryInterface.sequelize.query(
        `SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.TABLES 
         WHERE TABLE_NAME = 'QuestionOptions'`,
        { type: Sequelize.QueryTypes.SELECT }
      );

      if (questionOptionsExist[0].count > 0) {
        const existingOptions = await queryInterface.sequelize.query(
          'SELECT COUNT(*) as count FROM QuestionOptions WHERE isDeleted = 0',
          { type: Sequelize.QueryTypes.SELECT }
        );

        if (existingOptions[0].count > 0) {
          // Map common option texts to categories (case-insensitive)
          const categoryMappings = [
            // Very Positive
            { patterns: ['excellent', 'outstanding', 'strongly agree', 'very satisfied', 'love it', 'amazing', 'perfect'], categoryId: '11111111-1111-1111-1111-111111111111' },
            
            // Positive  
            { patterns: ['good', 'agree', 'satisfied', 'like it', 'yes', 'positive', 'happy', 'great'], categoryId: '22222222-2222-2222-2222-222222222222' },
            
            // Neutral
            { patterns: ['okay', 'ok', 'neutral', 'neither', 'average', 'maybe', 'unsure', 'fair'], categoryId: '33333333-3333-3333-3333-333333333333' },
            
            // Negative
            { patterns: ['bad', 'disagree', 'unsatisfied', 'dislike', 'no', 'negative', 'unhappy', 'poor'], categoryId: '44444444-4444-4444-4444-444444444444' },
            
            // Very Negative
            { patterns: ['terrible', 'awful', 'strongly disagree', 'very unsatisfied', 'hate it', 'horrible', 'worst'], categoryId: '55555555-5555-5555-5555-555555555555' }
          ];

          // Apply mappings
          for (const mapping of categoryMappings) {
            for (const pattern of mapping.patterns) {
              await queryInterface.sequelize.query(`
                UPDATE QuestionOptions 
                SET category_id = '${mapping.categoryId}' 
                WHERE LOWER(option_text) LIKE '%${pattern}%' 
                AND (category_id IS NULL OR category_id = '')
                AND isDeleted = 0
              `);
            }
          }

          // Set remaining unmapped options to NEUTRAL as default
          await queryInterface.sequelize.query(`
            UPDATE QuestionOptions 
            SET category_id = '33333333-3333-3333-3333-333333333333'
            WHERE (category_id IS NULL OR category_id = '')
            AND isDeleted = 0
          `);

          console.log('✅ Existing question options updated with categories');
        }
      }

      console.log('🎉 Answer categories seeder completed successfully!');
    } catch (error) {
      console.error('❌ Error during answer categories seeding:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    console.log('🔄 Rolling back seeder: Removing seeded answer categories...');
    
    // Remove only the seeded categories (by their specific IDs)
    await queryInterface.bulkDelete('AnswerCategories', {
      id: [
        '11111111-1111-1111-1111-111111111111',
        '22222222-2222-2222-2222-222222222222',
        '33333333-3333-3333-3333-333333333333',
        '44444444-4444-4444-4444-444444444444',
        '55555555-5555-5555-5555-555555555555'
      ]
    });
    
    console.log('✅ Seeded answer categories removed successfully');
    console.log('🔙 Seeder rollback completed');
  }
};
