'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    console.log('🚀 Starting migration: Creating AnswerCategories table...');
    
    try {
      await queryInterface.createTable('AnswerCategories', {
        id: {
          type: Sequelize.UUID,
          defaultValue: Sequelize.UUIDV4,
          primaryKey: true,
          allowNull: false
        },
        name: {
          type: Sequelize.STRING(50),
          allowNull: false,
          unique: true,
          comment: 'Category name (VERY_POSITIVE, POSITIVE, etc.)'
        },
        display_name: {
          type: Sequelize.STRING(100),
          allowNull: false,
          comment: 'Human readable name for display'
        },
        color_code: {
          type: Sequelize.STRING(7),
          allowNull: false,
          comment: 'Hex color code for graph visualization'
        },
        sort_order: {
          type: Sequelize.INTEGER,
          allowNull: false,
          defaultValue: 0,
          comment: 'Order for consistent sorting'
        },
        description: {
          type: Sequelize.TEXT,
          allowNull: true,
          comment: 'Detailed description of the category'
        },
        isActive: {
          type: Sequelize.BOOLEAN,
          defaultValue: true
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
        },
        createdBy: {
          type: Sequelize.UUID,
          allowNull: true
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
        },
        updatedBy: {
          type: Sequelize.UUID,
          allowNull: true
        },
        isDeleted: {
          type: Sequelize.BOOLEAN,
          defaultValue: false
        },
        deletedBy: {
          type: Sequelize.UUID,
          allowNull: true
        }
      });
      
      console.log('✅ AnswerCategories table created successfully');

      // Insert predefined categories
      console.log('🔧 Inserting predefined answer categories...');
      await queryInterface.bulkInsert('AnswerCategories', [
        {
          id: '11111111-1111-1111-1111-111111111111',
          name: 'VERY_POSITIVE',
          display_name: 'Very Positive',
          color_code: '#28a745',
          sort_order: 1,
          description: 'Excellent, Outstanding, Strongly Agree responses',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: '*************-2222-2222-************',
          name: 'POSITIVE',
          display_name: 'Positive',
          color_code: '#17a2b8',
          sort_order: 2,
          description: 'Good, Agree, Satisfied responses',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: '33333333-3333-3333-3333-333333333333',
          name: 'NEUTRAL',
          display_name: 'Neutral',
          color_code: '#ffc107',
          sort_order: 3,
          description: 'Okay, Neither Agree nor Disagree, Average responses',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: '44444444-4444-4444-4444-444444444444',
          name: 'NEGATIVE',
          display_name: 'Negative',
          color_code: '#fd7e14',
          sort_order: 4,
          description: 'Bad, Disagree, Unsatisfied responses',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: '55555555-5555-5555-5555-555555555555',
          name: 'VERY_NEGATIVE',
          display_name: 'Very Negative',
          color_code: '#dc3545',
          sort_order: 5,
          description: 'Terrible, Strongly Disagree, Very Unsatisfied responses',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ]);
      
      console.log('✅ Predefined categories inserted successfully');
      console.log('🎉 Migration completed: AnswerCategories table is ready!');
    } catch (error) {
      console.error('❌ Error during AnswerCategories migration:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    console.log('🔄 Rolling back migration: Dropping AnswerCategories table...');
    await queryInterface.dropTable('AnswerCategories');
    console.log('✅ AnswerCategories table dropped successfully');
    console.log('🔙 Migration rollback completed');
  }
};
